import React from 'react';
import { motion } from 'framer-motion';
import {
  User,
  CreditCard,
  GraduationCap,
  Briefcase,
  FileText,
  AlertTriangle,
  Camera,
  PenTool,
  Building,
  Hash,
  MapPin,
  Calendar,
  Award,
  Clock
} from 'lucide-react';

const PendingAlerts = ({ pendingData, loading, error }) => {
  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-500"></div>
          <span className="ml-2 text-gray-600">Loading pending alerts...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-xl p-6 mb-8">
        <div className="flex items-center">
          <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
          <span className="text-red-700">Error loading pending alerts: {error}</span>
        </div>
      </div>
    );
  }

  if (!pendingData) {
    return null;
  }

  // Calculate total pending items
  const getTotalPendingCount = () => {
    let total = 0;

    // Count Employee fields
    if (pendingData.Employee) {
      total += pendingData.Employee.length;
    }

    // Count Bank Details fields
    if (pendingData.EmpBankDetails) {
      total += pendingData.EmpBankDetails.length;
    }

    // Count Academic fields
    if (pendingData.Academics) {
      total += pendingData.Academics.length;
    }

    // Count Professional History fields
    if (pendingData.ProfessionalHistory) {
      total += pendingData.ProfessionalHistory.length;
    }

    // Count Documents
    if (pendingData.Documents) {
      total += pendingData.Documents.MissingCount || 0;
    }

    return total;
  };

  const totalPending = getTotalPendingCount();

  if (totalPending === 0) {
    return (
      <div className="bg-green-50 border border-green-200 rounded-xl p-6 mb-8">
        <div className="flex items-center">
          <div className="bg-green-100 p-2 rounded-full mr-3">
            <User className="h-5 w-5 text-green-600" />
          </div>
          <div>
            <h3 className="text-green-800 font-semibold">Profile Complete!</h3>
            <p className="text-green-600 text-sm">All required information has been submitted.</p>
          </div>
        </div>
      </div>
    );
  }

  // Section configurations
  const sectionConfigs = {
    Employee: {
      title: 'Personal Information',
      icon: User,
      color: 'bg-yellow-100 text-yellow-600',
      borderColor: 'border-yellow-200',
      fieldIcons: {
        ProfilePicture: Camera,
        Signature: PenTool
      }
    },
    EmpBankDetails: {
      title: 'Bank Details',
      icon: CreditCard,
      color: 'bg-yellow-100 text-yellow-600',
      borderColor: 'border-yellow-200',
      fieldIcons: {
        BankName: Building,
        AccountNumber: Hash,
        IFSCCode: Hash,
        BranchName: MapPin,
        CancelledCheque: FileText
      }
    },
    Academics: {
      title: 'Academic Information',
      icon: GraduationCap,
      color: 'bg-yellow-100 text-yellow-600',
      borderColor: 'border-yellow-200',
      fieldIcons: {
        Degree: Award,
        Institution: Building,
        YearOfPassing: Calendar,
        Grade: Award
      }
    },
    ProfessionalHistory: {
      title: 'Professional History',
      icon: Briefcase,
      color: 'bg-yellow-100 text-yellow-600',
      borderColor: 'border-yellow-200',
      fieldIcons: {
        PreviousCompany: Building,
        PreviousPosition: Briefcase,
        PreviousExperience: Clock,
        PreviousExperienceCeritificate: FileText
      }
    }
  };

  const formatFieldName = (fieldName) => {
    return fieldName
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, str => str.toUpperCase())
      .trim();
  };

  const renderSectionCard = (sectionKey, fields) => {
    if (!fields || fields.length === 0) return null;

    const config = sectionConfigs[sectionKey];
    if (!config) return null;

    const IconComponent = config.icon;

    return (
      <motion.div
        key={sectionKey}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className={`bg-yellow-50 border ${config.borderColor} rounded-lg p-2`}
      >
        <div className="flex items-center mb-3">
          <div className={`${config.color} p-2 rounded-lg mr-3`}>
            <IconComponent className="h-5 w-5" />
          </div>
          <div>
            <h4 className="font-semibold text-gray-800">{config.title}</h4>
            <p className="text-sm text-gray-600">{fields.length} field{fields.length > 1 ? 's' : ''} pending</p>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
          {fields.map((field, index) => {
            const FieldIcon = config.fieldIcons[field] || FileText;
            return (
              <div key={index} className="flex items-center bg-white rounded-md p-2 border border-yellow-200">
                <FieldIcon className="h-4 w-4 text-yellow-600 mr-2 flex-shrink-0" />
                <span className="text-sm text-gray-700 truncate">{formatFieldName(field)}</span>
              </div>
            );
          })}
        </div>
      </motion.div>
    );
  };

  const renderDocumentsCard = () => {
    if (!pendingData.Documents || pendingData.Documents.MissingCount === 0) return null;

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-yellow-50 border border-yellow-200 rounded-lg p-2"
      >
        <div className="flex items-center mb-3">
          <div className="bg-yellow-100 text-yellow-600 p-2 rounded-lg mr-3">
            <FileText className="h-5 w-5" />
          </div>
          <div>
            <h4 className="font-semibold text-gray-800">Documents</h4>
            <p className="text-sm text-gray-600">
              {pendingData.Documents.MissingCount} of {pendingData.Documents.TotalRequired} documents missing
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
          {pendingData.Documents.Details
            .filter(doc => doc.Status === 'Missing')
            .map((doc, index) => (
              <div key={index} className="flex items-center bg-white rounded-md p-2 border border-yellow-200">
                <FileText className="h-4 w-4 text-yellow-600 mr-2 flex-shrink-0" />
                <span className="text-sm text-gray-700 truncate">{doc.DocumentName}</span>
              </div>
            ))}
        </div>
      </motion.div>
    );
  };

  return (
    <div className="mb-8">
      {/* Header Alert */}
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-yellow-50 border border-yellow-200 rounded-xl p-2 mb-6"
      >
        <div className="flex items-start">
          <div className="bg-yellow-100 p-3 rounded-full mr-4 flex-shrink-0">
            <AlertTriangle className="h-6 w-6 text-yellow-600" />
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-yellow-800 mb-1">
              Profile Completion Required
            </h3>
            <p className="text-yellow-700 mb-3">
              You have <span className="font-semibold">{totalPending} pending items</span> that need your attention to complete your profile.
            </p>
          </div>
        </div>
      </motion.div>

      {/* Pending Items Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
        {/* Render section cards */}
        {Object.entries(pendingData).map(([sectionKey, fields]) => {
          if (sectionKey === 'Documents') return null; // Handle documents separately
          return renderSectionCard(sectionKey, fields);
        })}

        {/* Render documents card */}
        {renderDocumentsCard()}
      </div>
    </div>
  );
};

export default PendingAlerts;